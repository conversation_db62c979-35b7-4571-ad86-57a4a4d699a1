#!/usr/bin/env python
# -*- coding: utf-8 -*-

from excel_validator import ExcelValidator
import pandas as pd
import numpy as np
from typing import List, Dict, Any
import logging
from datetime import datetime
import re

logger = logging.getLogger('jiankao_app.extended_validator')

class ExtendedExcelValidator(ExcelValidator):
    """扩展的Excel验证器，增加了更多的验证规则"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.strict_validation_failed = False  # 严格验证是否失败
        self.warnings = []  # 警告信息列表

    def validate(self) -> bool:
        """
        执行所有验证步骤，包括基础验证和扩展验证

        返回:
            bool: 验证是否通过
        """
        try:
            # 更新进度：开始验证
            self._update_progress(0, "开始验证...")

            # 首先执行基础验证
            self._update_progress(10, "执行基础验证...")
            if not super().validate():
                self.is_valid = False
                self._update_progress(100, "基础验证失败")
                return False
            self._update_progress(30, "基础验证完成")

            # 执行严格验证
            self._update_progress(40, "执行严格验证规则...")
            self._validate_strict_rules()
            self._update_progress(70, "严格验证完成")
            
            # 执行警告级别的验证
            self._update_progress(80, "执行警告级别验证...")
            self._validate_warning_rules()
            self._update_progress(90, "警告验证完成")

            # 更新最终验证结果
            self.is_valid = not self.strict_validation_failed
            
            # 如果验证失败，生成错误报告
            if not self.is_valid:
                error_report_path = self._generate_error_report()
                if error_report_path:
                    self.validation_messages['error_report'] = error_report_path

            self._update_progress(100, "验证完成")
            return self.is_valid

        except Exception as e:
            logger.error(f"扩展验证过程发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"验证过程发生错误: {str(e)}")
            self.is_valid = False
            self._update_progress(100, "验证失败")
            return False

    def _validate_strict_rules(self) -> None:
        """执行严格验证规则"""
        try:
            # 1. 考试科目设置验证规则
            self._update_progress(42, "验证考试科目设置...")
            self._validate_subject_settings()

            # 2. 考场设置验证规则
            self._update_progress(47, "验证考场设置...")
            self._validate_room_settings()

            # 3. 监考员设置验证规则
            self._update_progress(52, "验证监考员设置...")
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            self._validate_proctor_settings(teacher_df)

            # 4. 总体验证规则
            self._update_progress(57, "验证总体规则...")
            self._validate_overall_rules()

            # 5. 原有的验证规则
            self._update_progress(62, "执行原有验证规则...")
            self._validate_teacher_basic_info(teacher_df)
            self._validate_session_limits(teacher_df)
            self._validate_data_format(teacher_df)

            # 如果没有错误，确保strict_validation_failed为False
            if len(self.errors) == 0:
                self.strict_validation_failed = False
                logger.info("严格验证通过")
                self._update_progress(65, "严格验证规则通过")

        except Exception as e:
            logger.error(f"执行严格验证规则时发生错误: {str(e)}", exc_info=True)
            self.strict_validation_failed = True
            self.errors.append(f"严格验证失败: {str(e)}")
            self._update_progress(65, "严格验证规则出错")

    def _validate_warning_rules(self) -> None:
        """执行警告级别的验证规则"""
        try:
            # 1. 检查监考员任教科目与考试科目的匹配度
            self._check_subject_matching()
            
            # 2. 检查考试时间分布是否合理
            self._check_exam_time_distribution()
            
            # 3. 检查监考员工作量分布
            self._check_workload_distribution()

        except Exception as e:
            logger.warning(f"执行警告级别验证时发生错误: {str(e)}", exc_info=True)
            self.warnings.append(f"警告级别验证过程出现异常: {str(e)}")

    def _check_subject_matching(self) -> None:
        """检查监考员任教科目与考试科目的匹配度"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')

            # 获取所有考试科目
            exam_subjects = set(subject_df['课程名称'].unique())
            
            # 收集所有不匹配的监考员信息
            unmatch_teachers = []
            
            # 检查每个监考员的任教科目
            for _, row in teacher_df.iterrows():
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    # 只有当任教科目不为'0'时才进行匹配检查
                    if subject_str != '0':
                        teacher_subjects = {s.strip() for s in subject_str.split(',') if s.strip()}
                        
                        # 如果监考员没有任教考试科目中的任何一门
                        if not teacher_subjects & exam_subjects:
                            unmatch_teachers.append({
                                'name': row['监考老师'],
                                'subjects': ','.join(teacher_subjects)
                            })
            
            # 如果存在不匹配的监考员，生成汇总警告信息
            if unmatch_teachers:
                # 按照教师姓名排序
                unmatch_teachers.sort(key=lambda x: x['name'])
                
                # 生成汇总信息
                teacher_count = len(unmatch_teachers)
                teacher_list = '\n'.join([
                    f"- {t['name']}（任教科目：{t['subjects']}）" 
                    for t in unmatch_teachers
                ])
                
                warning_msg = (
                    f"发现{teacher_count}位监考员的任教科目与考试科目无重叠，可能不熟悉考试内容：\n"
                    f"{teacher_list}\n"
                    f"建议检查这些监考员的安排是否合理。"
                )
                
                self.add_warning(warning_msg)

        except Exception as e:
            logger.warning(f"检查科目匹配度时发生错误: {str(e)}", exc_info=True)

    def _check_exam_time_distribution(self) -> None:
        """检查考试时间分布是否合理"""
        try:
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')
            
            # 将时间字符串转换为datetime - 支持新的时间格式
            try:
                # 尝试新格式 yyyy/mm/dd hh:mm
                subject_df['开始时间'] = pd.to_datetime(subject_df['开始时间'], format='%Y/%m/%d %H:%M').dt.time
                subject_df['结束时间'] = pd.to_datetime(subject_df['结束时间'], format='%Y/%m/%d %H:%M').dt.time
            except ValueError:
                try:
                    # 回退到旧格式 HH:MM
                    subject_df['开始时间'] = pd.to_datetime(subject_df['开始时间'], format='%H:%M').dt.time
                    subject_df['结束时间'] = pd.to_datetime(subject_df['结束时间'], format='%H:%M').dt.time
                except ValueError:
                    # 如果都失败，跳过时间分布检查
                    logger.warning("无法解析时间格式，跳过考试时间分布检查")
                    return
            
            # 检查是否存在考试时间过于集中的情况
            time_slots = []
            for _, row in subject_df.iterrows():
                time_slots.append((row['开始时间'], row['结束时间']))
            
            # 统计同时进行的考试数量
            max_concurrent = 0
            for i, (start1, end1) in enumerate(time_slots):
                concurrent = 1
                for j, (start2, end2) in enumerate(time_slots):
                    if i != j:
                        if (start1 <= start2 < end1) or (start2 <= start1 < end2):
                            concurrent += 1
                max_concurrent = max(max_concurrent, concurrent)
            
            if max_concurrent > len(subject_df) / 2:
                self.add_warning(
                    f"考试时间分布过于集中，最多有{max_concurrent}场考试同时进行，建议适当分散考试时间"
                )

        except Exception as e:
            logger.warning(f"检查考试时间分布时发生错误: {str(e)}", exc_info=True)

    def _check_workload_distribution(self) -> None:
        """检查监考员工作量分布是否合理"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            
            # 计算平均场次限制
            avg_limit = teacher_df['场次限制'].mean()
            std_limit = teacher_df['场次限制'].std()
            
            # 检查场次限制差异过大的情况
            for _, row in teacher_df.iterrows():
                if abs(row['场次限制'] - avg_limit) > 2 * std_limit:
                    self.add_warning(
                        f"监考员 {row['监考老师']} 的场次限制({row['场次限制']})与平均值({avg_limit:.1f})差异较大，" +
                        "建议适当调整以保证工作量均衡"
                    )

        except Exception as e:
            logger.warning(f"检查工作量分布时发生错误: {str(e)}", exc_info=True)

    def _validate_teacher_basic_info(self, teacher_df: pd.DataFrame) -> None:
        """验证监考员基本信息"""
        try:
            # 检查是否有重复的监考员
            duplicate_teachers = teacher_df[teacher_df['监考老师'].duplicated()]['监考老师'].unique()
            if len(duplicate_teachers) > 0:
                self.errors.append(f"发现重复的监考员: {', '.join(duplicate_teachers)}")
                self.strict_validation_failed = True

            # 检查每一行的监考员信息
            for idx, row in teacher_df.iterrows():
                row_num = idx + 2  # Excel行号从1开始，且有表头
                
                # 检查监考员姓名是否为空
                if pd.isna(row['监考老师']) or str(row['监考老师']).strip() == '':
                    self.errors.append(f"监考员设置表第{row_num}行监考老师姓名不能为空")
                    self.strict_validation_failed = True
                    continue

                # 检查任教科目格式
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    if subject_str != '0' and not all(s.strip() for s in subject_str.split(',')):
                        self.errors.append(f"监考员设置表第{row_num}行任教科目格式无效: {subject_str}")
                        self.strict_validation_failed = True

                # 检查必监考科目格式
                if '必监考科目' in row and pd.notna(row['必监考科目']):
                    required_subjects = str(row['必监考科目']).strip()
                    if required_subjects != '0' and not all(s.strip() for s in required_subjects.split(',')):
                        self.errors.append(f"监考员设置表第{row_num}行必监考科目格式无效: {required_subjects}")
                        self.strict_validation_failed = True

                # 检查不监考科目格式
                if '不监考科目' in row and pd.notna(row['不监考科目']):
                    unavailable_subjects = str(row['不监考科目']).strip()
                    if unavailable_subjects != '0' and not all(s.strip() for s in unavailable_subjects.split(',')):
                        self.errors.append(f"监考员设置表第{row_num}行不监考科目格式无效: {unavailable_subjects}")
                        self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证监考员基本信息时发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"验证监考员信息时发生错误: {str(e)}")
            self.strict_validation_failed = True

    def _validate_session_limits(self, teacher_df: pd.DataFrame) -> None:
        """验证场次限制的有效性"""
        # 检查场次限制是否为正整数
        invalid_limits = teacher_df[~teacher_df['场次限制'].apply(lambda x: isinstance(x, (int, float)) and x > 0)]
        if not invalid_limits.empty:
            rows = invalid_limits.index.tolist()
            error_msg = f"以下行的场次限制必须为正整数: {[r+2 for r in rows]}"
            self.add_error_detail('监考员设置', rows[0]+2, '场次限制', 
                                str(invalid_limits.iloc[0]['场次限制']), '数据格式错误', error_msg)
            self.strict_validation_failed = True

        # 检查场次限制是否为0
        zero_limits = teacher_df[teacher_df['场次限制'] == 0]
        if not zero_limits.empty:
            rows = zero_limits.index.tolist()
            error_msg = f"以下行的场次限制不能为0: {[r+2 for r in rows]}"
            self.add_error_detail('监考员设置', rows[0]+2, '场次限制', '0', '数据值错误', error_msg)
            self.strict_validation_failed = True

        # 检查场次限制总和是否大于等于考试科目总场次
        subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')
        total_sessions = len(subject_df)
        total_limits = teacher_df['场次限制'].sum()
        if total_limits < total_sessions:
            error_msg = f"监考员场次限制总和({total_limits})必须大于等于考试总场次数({total_sessions})"
            self.add_error_detail('监考员设置', 2, '场次限制', str(total_limits), '数据值错误', error_msg)
            self.strict_validation_failed = True

    def _validate_data_format(self, teacher_df: pd.DataFrame) -> None:
        """验证数据格式的规范性"""
        try:
            # 检查序号的连续性
            expected_seq = range(1, len(teacher_df) + 1)
            actual_seq = teacher_df['序号'].tolist()
            if not all(a == e for a, e in zip(actual_seq, expected_seq)):
                error_msg = "序号不连续或不从1开始"
                self.errors.append(error_msg)
                self.add_error_detail('监考员设置', 1, '序号', str(actual_seq), '序号错误', error_msg)
                self.strict_validation_failed = True

            # 检查任教科目格式
            for idx, row in teacher_df.iterrows():
                if pd.notna(row['任教科目']):
                    subject_str = str(row['任教科目']).strip()
                    # 如果值不是'0'且不是'nan'，则进行格式验证
                    if subject_str != '0' and subject_str.lower() != 'nan':
                        if not bool(re.match(r'^[\u4e00-\u9fa5,]+$', subject_str)):
                            error_msg = f"监考员设置表第{idx+1}行（{row['监考老师']}）的任教科目格式无效（应为中文科目名，多个科目用逗号分隔）"
                            self.errors.append(error_msg)
                            self.add_error_detail('监考员设置', idx+1, '任教科目', subject_str, '格式错误', error_msg)
                            self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证数据格式时发生错误: {str(e)}", exc_info=True)
            self.strict_validation_failed = True
            self.errors.append(f"验证数据格式时发生错误: {str(e)}")

    def _validate_subject_sheet(self):
        """验证考试科目设置表（旧版本兼容方法）"""
        try:
            sheet = pd.read_excel(self.workbook, sheet_name='考试科目设置')

            # 修改为新的验证规则：必须包含课程代码、课程名称、开始时间、结束时间，内容允许为空值
            required_columns = ['课程代码', '课程名称', '开始时间', '结束时间']
            missing_columns = [col for col in required_columns if col not in sheet.columns]
            if missing_columns:
                self.errors.append(f"考试科目设置表缺少必要的列: {', '.join(missing_columns)}")
                return

            # 检查每一行的数据（仅对非空值进行格式验证）
            time_slots = []  # 用于存储时间段，检查重叠
            for index, row in sheet.iterrows():
                row_num = index + 2  # Excel行号从1开始，且有表头

                # 检查时间格式（仅对非空值进行格式验证）
                start_time_str = str(row.get('开始时间', '')).strip()
                end_time_str = str(row.get('结束时间', '')).strip()

                # 只对有效的时间进行格式检查和重叠检查
                if (start_time_str and end_time_str and
                    start_time_str.lower() != 'nan' and end_time_str.lower() != 'nan' and
                    start_time_str != '' and end_time_str != ''):

                    try:
                        # 支持多种时间格式
                        start_dt = None
                        end_dt = None

                        # 尝试解析 yyyy/mm/dd hh:mm 格式
                        if '/' in start_time_str and ' ' in start_time_str:
                            start_dt = datetime.strptime(start_time_str, '%Y/%m/%d %H:%M')
                        elif ':' in start_time_str:
                            # HH:MM 格式，需要配合考试日期
                            start_time = datetime.strptime(start_time_str, '%H:%M')
                            # 如果有考试日期列，使用它；否则使用默认日期
                            if '考试日期' in row and pd.notna(row['考试日期']):
                                date_str = str(row['考试日期']).strip()
                                try:
                                    exam_date = datetime.strptime(date_str, '%Y/%m/%d')
                                    start_dt = datetime.combine(exam_date.date(), start_time.time())
                                except ValueError:
                                    start_dt = datetime.combine(datetime(2025, 1, 1).date(), start_time.time())
                            else:
                                start_dt = datetime.combine(datetime(2025, 1, 1).date(), start_time.time())

                        # 尝试解析结束时间
                        if '/' in end_time_str and ' ' in end_time_str:
                            end_dt = datetime.strptime(end_time_str, '%Y/%m/%d %H:%M')
                        elif ':' in end_time_str:
                            # HH:MM 格式
                            end_time = datetime.strptime(end_time_str, '%H:%M')
                            if start_dt:
                                end_dt = datetime.combine(start_dt.date(), end_time.time())
                            else:
                                end_dt = datetime.combine(datetime(2025, 1, 1).date(), end_time.time())

                        if start_dt and end_dt:
                            # 检查开始时间是否早于结束时间
                            if start_dt >= end_dt:
                                self.errors.append(f"考试科目设置表第{row_num}行结束时间必须晚于开始时间")
                                continue

                            # 检查时间重叠（仅检查同一天的考试）
                            for other_start, other_end, other_row, other_subj in time_slots:
                                if other_start.date() == start_dt.date():  # 只检查同一天的考试
                                    if start_dt < other_end and end_dt > other_start:
                                        subject_name = row.get('课程名称', f'第{row_num}行')
                                        self.errors.append(
                                            f"考试科目设置表第{row_num}行（{subject_name}）的考试时间与第{other_row}行（{other_subj}）时间重合"
                                        )

                            # 添加到时间段列表
                            time_slots.append((start_dt, end_dt, row_num, row.get('课程名称', f'第{row_num}行')))

                    except ValueError:
                        self.errors.append(f"考试科目设置表第{row_num}行时间格式错误，应为 HH:MM 或 yyyy/mm/dd hh:mm 格式")
                        continue

            # 存储科目数据
            self.subjects_data = sheet.to_dict('records')

        except Exception as e:
            self.errors.append(f"验证考试科目设置表时发生错误: {str(e)}")
            logger.error(f"验证考试科目设置表时发生错误: {str(e)}", exc_info=True)

    def _validate_subject_settings(self) -> None:
        """验证考试科目设置规则"""
        try:
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')

            # 1. 检查必需的表头（课程代码、课程名称、开始时间、结束时间）
            # 内容允许为空值，但表头必须包含这四列，可以有其他列
            required_columns = ['课程代码', '课程名称', '开始时间', '结束时间']
            missing_columns = [col for col in required_columns if col not in subject_df.columns]
            if missing_columns:
                self.errors.append(f"考试科目设置表缺少必要的列头: {', '.join(missing_columns)}")
                self.strict_validation_failed = True
                return

            # 2. 检查时间格式：yyyy/mm/dd hh:mm（仅对非空值进行格式验证）
            for idx, row in subject_df.iterrows():
                row_num = idx + 2

                # 检查开始时间格式（允许空值）
                start_time = str(row.get('开始时间', '')).strip()
                if start_time and start_time.lower() != 'nan' and start_time != '':
                    if not self._validate_datetime_format(start_time):
                        self.errors.append(f"考试科目设置表第{row_num}行开始时间格式错误，应为 yyyy/mm/dd hh:mm 格式")
                        self.strict_validation_failed = True

                # 检查结束时间格式（允许空值）
                end_time = str(row.get('结束时间', '')).strip()
                if end_time and end_time.lower() != 'nan' and end_time != '':
                    if not self._validate_datetime_format(end_time):
                        self.errors.append(f"考试科目设置表第{row_num}行结束时间格式错误，应为 yyyy/mm/dd hh:mm 格式")
                        self.strict_validation_failed = True

            # 3. 检查时间段重叠（仅对有效时间进行检查）
            self._check_time_overlap(subject_df)

        except Exception as e:
            logger.error(f"验证考试科目设置时发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"考试科目设置验证失败: {str(e)}")
            self.strict_validation_failed = True

    def _validate_datetime_format(self, datetime_str: str) -> bool:
        """验证日期时间格式 yyyy/mm/dd hh:mm"""
        try:
            datetime.strptime(datetime_str, '%Y/%m/%d %H:%M')
            return True
        except ValueError:
            return False

    def _check_time_overlap(self, subject_df: pd.DataFrame) -> None:
        """检查考试时间段重叠（仅对有效时间进行检查）"""
        time_slots = []

        for idx, row in subject_df.iterrows():
            row_num = idx + 2
            start_time_str = str(row.get('开始时间', '')).strip()
            end_time_str = str(row.get('结束时间', '')).strip()

            # 只对非空且有效的时间进行检查
            if (start_time_str and end_time_str and
                start_time_str.lower() != 'nan' and end_time_str.lower() != 'nan' and
                start_time_str != '' and end_time_str != ''):
                try:
                    start_time = datetime.strptime(start_time_str, '%Y/%m/%d %H:%M')
                    end_time = datetime.strptime(end_time_str, '%Y/%m/%d %H:%M')

                    if start_time >= end_time:
                        self.errors.append(f"考试科目设置表第{row_num}行开始时间不能晚于或等于结束时间")
                        self.strict_validation_failed = True
                        continue

                    # 检查与已有时间段的重叠
                    for existing_slot in time_slots:
                        if self._time_slots_overlap(start_time, end_time, existing_slot['start'], existing_slot['end']):
                            subject_name = row.get('课程名称', f'第{row_num}行')
                            existing_subject = existing_slot['subject']
                            self.errors.append(f"考试科目 '{subject_name}' 的时间段与 '{existing_subject}' 重叠")
                            self.strict_validation_failed = True

                    time_slots.append({
                        'start': start_time,
                        'end': end_time,
                        'subject': row.get('课程名称', f'第{row_num}行')
                    })

                except ValueError:
                    # 时间格式错误已在前面检查过
                    pass

    def _time_slots_overlap(self, start1, end1, start2, end2) -> bool:
        """检查两个时间段是否重叠（同一天）"""
        # 只有在同一天的时间段才检查重叠
        if start1.date() == start2.date():
            return start1 < end2 and start2 < end1
        return False

    def get_validation_messages(self) -> Dict[str, Any]:
        """
        获取验证消息，包括错误、警告和错误报告路径

        返回:
            Dict: 包含验证结果的字典
        """
        messages = super().get_validation_messages()
        if hasattr(self, 'validation_messages') and 'error_report' in self.validation_messages:
            messages['error_report'] = self.validation_messages['error_report']
        return messages

    def _validate_room_settings(self) -> None:
        """验证考场设置规则"""
        try:
            room_df = pd.read_excel(self.workbook, sheet_name='考场设置')
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')

            # 1. 检查第一列是否为考场
            if room_df.empty or room_df.columns[0] != '考场':
                self.errors.append("考场设置表第一列列头必须是'考场'")
                self.strict_validation_failed = True
                return

            # 2. 检查考试科目是否与设置的考试科目一致
            exam_subjects = set(subject_df['课程名称'].unique()) if '课程名称' in subject_df.columns else set()
            room_subjects = set(room_df.columns[1:])  # 除了考场列的其他列

            undefined_subjects = room_subjects - exam_subjects
            if undefined_subjects:
                self.errors.append(f"考场设置表中包含未在考试科目设置中定义的科目: {', '.join(undefined_subjects)}")
                self.strict_validation_failed = True

            # 3. 检查不能所有科目的考场监考人数都是"N/A"
            for idx, row in room_df.iterrows():
                row_num = idx + 2
                room_name = row.get('考场', f'第{row_num}行')

                # 检查该考场是否所有科目都是N/A
                subject_values = []
                for col in room_df.columns[1:]:  # 跳过考场列
                    value = str(row.get(col, '')).strip().upper()
                    subject_values.append(value)

                if all(val in ['N/A', 'NA', ''] for val in subject_values):
                    self.errors.append(f"考场设置表第{row_num}行（{room_name}）所有科目的监考人数都是'N/A'，至少需要一个科目有有效的监考人数")
                    self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证考场设置时发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"考场设置验证失败: {str(e)}")
            self.strict_validation_failed = True

    def _validate_proctor_settings(self, teacher_df: pd.DataFrame) -> None:
        """验证监考员设置规则"""
        try:
            # 1. 检查必需的表头
            required_columns = ['序号', '监考老师', '任教科目', '必监考科目', '不监考科目', '必监考考场', '不监考考场', '场次限制']
            missing_columns = [col for col in required_columns if col not in teacher_df.columns]
            if missing_columns:
                self.errors.append(f"监考员设置表缺少必要的列头: {', '.join(missing_columns)}")
                self.strict_validation_failed = True
                return

            # 获取所有考试科目
            subject_df = pd.read_excel(self.workbook, sheet_name='考试科目设置')
            all_subjects = set(subject_df['课程名称'].unique()) if '课程名称' in subject_df.columns else set()
            total_subjects_count = len(all_subjects)

            # 2. 检查每个监考员的规则
            for idx, row in teacher_df.iterrows():
                row_num = idx + 2
                proctor_name = row.get('监考老师', f'第{row_num}行')

                # 解析必监考科目和不监考科目
                required_subjects = self._parse_subject_list(row.get('必监考科目', ''))
                unavailable_subjects = self._parse_subject_list(row.get('不监考科目', ''))
                required_rooms = self._parse_subject_list(row.get('必监考考场', ''))
                unavailable_rooms = self._parse_subject_list(row.get('不监考考场', ''))

                # 检查必监考科目和不监考科目不能有相同科目
                common_subjects = set(required_subjects) & set(unavailable_subjects)
                if common_subjects:
                    self.errors.append(f"监考员设置表第{row_num}行（{proctor_name}）必监考科目和不监考科目不能有相同科目: {', '.join(common_subjects)}")
                    self.strict_validation_failed = True

                # 检查必监考考场和不监考考场不能有相同考场
                common_rooms = set(required_rooms) & set(unavailable_rooms)
                if common_rooms:
                    self.errors.append(f"监考员设置表第{row_num}行（{proctor_name}）必监考考场和不监考考场不能有相同考场: {', '.join(common_rooms)}")
                    self.strict_validation_failed = True

                # 检查场次限制
                session_limit = row.get('场次限制', 0)
                try:
                    session_limit = int(session_limit) if pd.notna(session_limit) else 0
                except (ValueError, TypeError):
                    session_limit = 0

                # 检查必监考科目数量加场次限制不能超过考试所有科目总和
                required_count = len(required_subjects)
                if required_count + session_limit > total_subjects_count:
                    self.errors.append(f"监考员设置表第{row_num}行（{proctor_name}）必监考科目数量({required_count})加场次限制({session_limit})不能超过考试所有科目总和({total_subjects_count})")
                    self.strict_validation_failed = True

                # 检查必监考科目和不监考科目数量不能超过考试所有科目总和
                unavailable_count = len(unavailable_subjects)
                if required_count + unavailable_count > total_subjects_count:
                    self.errors.append(f"监考员设置表第{row_num}行（{proctor_name}）必监考科目数量({required_count})和不监考科目数量({unavailable_count})不能超过考试所有科目总和({total_subjects_count})")
                    self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证监考员设置时发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"监考员设置验证失败: {str(e)}")
            self.strict_validation_failed = True

    def _parse_subject_list(self, subject_str) -> List[str]:
        """解析科目列表字符串"""
        if pd.isna(subject_str) or str(subject_str).strip() == '':
            return []

        # 支持多种分隔符
        import re
        separators = r'[。，,;|:\s\t\n.\-_/\\()\[\]{}<>*&^%$#@!~`+=?]+'
        items = re.split(separators, str(subject_str))
        return [item.strip() for item in items if item.strip()]

    def _validate_overall_rules(self) -> None:
        """验证总体规则"""
        try:
            teacher_df = pd.read_excel(self.workbook, sheet_name='监考员设置')
            room_df = pd.read_excel(self.workbook, sheet_name='考场设置')

            # 计算所有监考员的场次限制总和
            total_session_limits = 0
            for idx, row in teacher_df.iterrows():
                session_limit = row.get('场次限制', 0)
                try:
                    session_limit = int(session_limit) if pd.notna(session_limit) else 0
                    total_session_limits += session_limit
                except (ValueError, TypeError):
                    # 场次限制格式错误已在其他地方检查
                    pass

            # 计算考场设置中所有科目考场的监考人数总和
            total_supervision_needed = 0
            for idx, row in room_df.iterrows():
                for col in room_df.columns[1:]:  # 跳过考场列
                    value = str(row.get(col, '')).strip().upper()
                    if value not in ['N/A', 'NA', ''] and value.isdigit():
                        total_supervision_needed += int(value)

            # 检查监考员场次限制总和是否大于等于考场监考人数总和
            if total_session_limits < total_supervision_needed:
                self.errors.append(f"所有监考员的场次限制总和({total_session_limits})需要大于等于考场设置中所有科目考场的监考人数总和({total_supervision_needed})")
                self.strict_validation_failed = True

        except Exception as e:
            logger.error(f"验证总体规则时发生错误: {str(e)}", exc_info=True)
            self.errors.append(f"总体规则验证失败: {str(e)}")
            self.strict_validation_failed = True